type RootLayoutProps = {
  children: React.ReactNode
}

export default function RootLayout({ children }: RootLayoutProps) {
  return (
    <div className="px-4 py-2">
      <div className="flex flex-col space-y-2">
        <h1 className="text-ctx-content-title ts-heading-md">Grupos</h1>
        <p className="text-ctx-content-base ts-paragraph-xs">
          <PERSON><PERSON><PERSON><PERSON>, creden<PERSON> e edite os Grupos
        </p>
      </div>

      <div className="mt-8">{children}</div>
    </div>
  )
}
