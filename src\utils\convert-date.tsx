export function convertDate(date: Date) {
  return new Intl.DateTimeFormat('pt-BR', {
    year: 'numeric',
    month: 'long',
  })
    .format(new Date(date))
    .replace(' de', '')
}

export function convertDateToCalendar(date: Date) {
  return new Intl.DateTimeFormat('pt-BR', {
    year: 'numeric',
    month: 'numeric',
    day: 'numeric',
  })
    .format(new Date(date))
    .replace(' de', '')
}
