'use client'

import { Stepper, StepperContent, StepperItem } from '@ads/components-react'
import { useState } from 'react'

import { Step1 } from './step1'

export default function CriarTrilha() {
  const [activeStep, setActiveStep] = useState(0)

  return (
    <div className="m-auto flex w-full flex-col gap-8 px-6 py-8">
      <div>
        <h1 className="text-ctx-content-title ts-heading-md">
          Trilha de aprendizagem
        </h1>
        <span className="text-ctx-content-base ts-paragraph-xs">
          <PERSON><PERSON><PERSON><PERSON>, acompanhe e analise o progresso de desenvolvimento dos
          colaboradores em tempo real.
        </span>
      </div>
      <div className="flex w-full justify-center">
        <Stepper activeStep={activeStep}>
          <StepperItem title="Option 1" />
          <StepperItem title="Option 2" />
          <StepperItem title="Option 3" />
        </Stepper>
      </div>
      <div>
        <StepperContent activeStep={activeStep} index={0} className="w-full">
          <Step1 />
        </StepperContent>
        <StepperContent activeStep={activeStep} index={1}>
          <p>Content 2</p>
        </StepperContent>
        <StepperContent activeStep={activeStep} index={2}>
          <p>Content 3</p>
        </StepperContent>
      </div>
    </div>
  )
}
