'use client'

import { But<PERSON> } from '@ads/components-react'
import { Sparkles } from 'lucide-react'
import { useState } from 'react'
import { useQuery } from 'react-query'
import { useStore } from 'zustand'

import { Filter } from '@/components/filter'
import { PageLayout } from '@/components/layouts/page-layout'
import { StudyPlanTable } from '@/components/tables/study-plan'
import { useAuth } from '@/contexts/AuthContext'
import { getStudyPlans } from '@/http/study-plan'
import { useFilterStore } from '@/store/useFilterStore'

export default function Trilhas() {
  const { user } = useAuth()
  const { onChangeSearch, onChangeStatus } = useFilterStore()
  const [currentPage, setCurrentPage] = useState(1)
  const { search, status } = useStore(useFilterStore)

  const handleStatus = (status:  string) => {
    if (status === 'ALL' || status === 'undefined') {
      onChangeStatus(undefined)
    } else {
      onChangeStatus(status)
    }
  }

  const {
    data: studyPlans,
    isLoading,
    isFetching,
  } = useQuery({
    queryKey: ['getStudyPlansGql', currentPage, user, search, status],
    queryFn: () =>
      getStudyPlans({
        page: currentPage,
        company_id: user?.metadata.company_id ?? 0,
        limit: 10,
        name: search ?? '',
        onlyPDI: false,
        user_id: Number(user?.id),
        end_date: status === undefined ? new Date() : undefined,
        status: status,
      }),
    keepPreviousData: true,
    staleTime: 60 * 1000 * 10, // 10 minutes
  })

  console.log(studyPlans)

  return (
    <PageLayout
      title="Trilhas de aprendizagem"
      description="Gerencie, acompanhe e analise o progresso de desenvolvimento dos colaboradores em tempo real."
    >
      <div className="mb-8 flex items-center justify-between">
        <Filter
          pageKey="trilhas"
          onChangeStatus={onChangeStatus}
          onChangeSearch={onChangeSearch}
        />
        <Button hierarchy="secondary" size="md" className="mr-4">
          Criar nova Trilha
        </Button>
        <Button trailingIcon={Sparkles}>Criar trilha com IA</Button>
      </div>

      <StudyPlanTable
        isLoading={isLoading}
        isFetchingNewPage={isFetching}
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        studyPlans={
          studyPlans?.studyPlans ?? { data: [], total: 0, perPage: 0 }
        }
      />
    </PageLayout>
  )
}
