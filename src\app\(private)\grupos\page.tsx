'use client'

import { But<PERSON>, <PERSON> } from '@ads/components-react'
import { useRouter } from 'next/navigation'

export default function Group() {
  const { push } = useRouter()

  function handleNewGroup() {
    push('/grupos/novo')
  }

  return (
    <>
      <div className="mt-8">
        <div className="flex justify-between space-x-2">
          <div className="w-full md:w-[600px] [&_button.at-rounded-component-iconButton-border-radius]:hidden">
            <Search
              size="md"
              placeholder="Buscar por equipe"
              className="bg-ctx-interactive-secondary [&_button.at-rounded-component-iconButton-border-radius]:hidden"
              handleChange={() => {}}
              query={''}
            />
          </div>

          <Button hierarchy="primary" size="md" onClick={handleNewGroup}>
            Criar nova Equipe
          </Button>
        </div>

        <div></div>
      </div>
    </>
  )
}
