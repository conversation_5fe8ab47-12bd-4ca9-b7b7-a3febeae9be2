import z from 'zod'

export const step1Schema = z.object({
  name: z.string().email({ message: 'Please enter a valid email address' }),
  description: z.string().min(3, 'First name must be at least 3 characters'),
  isMandatory: z.boolean().optional(),
})

export const step2Schema = z.object({
  country: z
    .string()
    .min(2, 'Country must be at least 2 characters')
    .max(100, 'Country must be less than 100 characters'),
  city: z.string().min(2, 'City must be at least 2 characters'),
})
