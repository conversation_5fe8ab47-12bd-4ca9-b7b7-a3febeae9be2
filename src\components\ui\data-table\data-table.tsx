'use client'
import { Pagination } from '@ads/components-react'
import { Loader2 } from 'lucide-react'

import { cn } from '@/lib/utils'

import { TableSkeleton } from '../../loaders/skeletons/table'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../table'
import {
  BaseTableRow,
  DataTableProps,
  PaginationInfo,
  TableColumn,
} from './types'

export type { DataTableProps, PaginationInfo, TableColumn }

function getCellValue<T>(row: T, column: TableColumn<T>): React.ReactNode {
  if (typeof column.accessor === 'function') {
    return column.accessor(row)
  }

  if (column.accessor && typeof column.accessor === 'string') {
    return row[column.accessor] as React.ReactNode
  }

  return row[column.key as keyof T] as React.ReactNode
}

function EmptyState({
  message,
  colSpan,
}: {
  message: string
  colSpan: number
}) {
  return (
    <TableRow>
      <TableCell
        colSpan={colSpan}
        className="py-8 text-center text-ctx-content-base ts-paragraph-xs"
      >
        {message}
      </TableCell>
    </TableRow>
  )
}

export function DataTable<T extends BaseTableRow>({
  data,
  columns,
  pagination,
  onPageChange,
  loading = false,
  emptyMessage = 'Nenhum resultado encontrado.',
  className,
  tableClassName,
  showPagination = true,
  isFetchingNewPage,
}: DataTableProps<T>) {
  return (
    <div className={cn('w-full rounded-xl bg-white p-6 shadow-md', className)}>
      <div className="rounded-xl border">
        <Table className={tableClassName}>
          <TableHeader className="text-ctx-content-title ts-paragraph-xxs">
            <TableRow>
              {columns.map((column) => (
                <TableHead
                  key={column.key}
                  className={cn('px-6', column.headerClassName)}
                >
                  {column.header}
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableSkeleton columns={columns as []} />
            ) : data.length === 0 ? (
              <EmptyState message={emptyMessage} colSpan={columns.length} />
            ) : (
              data.map((row, index) => (
                <TableRow
                  key={row.id}
                  className={`text-ctx-content-base ts-subtitle-xxs ${index % 2 === 0 ? 'bg-ctx-layout-body' : 'bg-base-gray-100'} h-20`}
                >
                  {columns.map((column) => (
                    <TableCell
                      key={column.key}
                      className={cn('px-6', column.className)}
                    >
                      {getCellValue(row, column)}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
        {showPagination && !loading && (
          <div className="flex w-full justify-center py-4">
            <Pagination
              controlText={{ previous: 'Anterior', next: 'Próximo' }}
              activeIndex={pagination.currentPage - 1}
              setActiveIndex={(e) => onPageChange((e as number) + 1)}
              pageAmount={pagination.totalPages}
              ellipsisLabel="Buscar mais itens"
            />
            {isFetchingNewPage && (
              <div className="flex items-center justify-center">
                <Loader2 className="h-4 w-4 animate-spin text-base-primary-500" />
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
