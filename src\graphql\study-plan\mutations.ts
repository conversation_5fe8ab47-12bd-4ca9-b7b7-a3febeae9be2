import { gql } from 'graphql-request'

export const CREATE_STUDY_PLAN = gql`
  mutation CreateStudyPlan(
    $name: String!
    $description: String!
    $courses: [StudyPlanCoursePivot!]!
    $company_id: Float!
    $squad_ids: [Float!]
    $user_ids: [Float!]
    $is_pdi: Boolean
    $is_for_all_users: Boolean
    $is_for_all_squads: Boolean
    $is_for_all_courses: Boolean
  ) {
    createStudyPlan(
      data: {
        name: $name
        description: $description
        courses: $courses
        company_id: $company_id
        squad_ids: $squad_ids
        user_ids: $user_ids
        is_pdi: $is_pdi
        is_for_all_users: $is_for_all_users
        is_for_all_squads: $is_for_all_squads
        is_for_all_courses: $is_for_all_courses
      }
    ) {
      id
    }
  }
`

export const DELETE_STUDY_PLAN = gql`
  mutation deleteStudyPlan($id: Float!) {
    deleteStudyPlan(id: $id)
  }
`
