'use client'

import {
  <PERSON><PERSON>,
  IconButton,
  LinkButton,
  Search,
  SelectInput,
  SelectInputItem,
} from '@ads/components-react'
import { zodResolver } from '@hookform/resolvers/zod'
import { AnimatePresence, motion } from 'framer-motion'
import { useEffect, useRef, useState } from 'react'
import { Controller, useForm } from 'react-hook-form'
import { CiFilter } from 'react-icons/ci'
import z from 'zod'

import { useDebouncedValue } from '@/hooks/use-debounce'
import { useFilterStore } from '@/store/useFilterStore'

export const filterSchema = z.object({
  search: z.string().optional().nullable(),
  status: z.string().optional(),
})

export type FilterFormValues = z.infer<typeof filterSchema>

const statusOptions = [
  {
    value: 'ALL',
    label: 'Todos Status',
  },
  {
    value: 'ACTIVE',
    label: 'Ativo',
  },
  {
    value: 'INACTIVE',
    label: 'Inativo',
  },
  {
    value: 'FINISHED',
    label: 'Finalizado',
  },
]

const defaultFormValues: FilterFormValues = {
  search: '',
  status: 'ALL',
}

interface FilterProps {
  placeholder?: string
  pageKey: string
  onChangeStatus?: (status: string) => void
  onChangeSearch?: (search: string) => void
}

export function Filter({
  placeholder = 'Busca por Trilha',
  pageKey,
  onChangeStatus,
  onChangeSearch,
}: FilterProps) {
  const [isOpenFilter, setIsOpenFilter] = useState(false)
  const [isFilter, setIsFilter] = useState<boolean>(false)

  const isSelectOpenRef = useRef(false)
  const filterRef = useRef<HTMLDivElement | null>(null)
  const filterToggleButtonMobileRef = useRef<HTMLButtonElement | null>(null)
  const filterToggleButtonRef = useRef<HTMLButtonElement | null>(null)
  const initializedRef = useRef(false)

  const {
    search: contextSearch,
    status: contextStatus,
    setCurrentPage,
  } = useFilterStore()

  const { control, handleSubmit, reset, watch } = useForm<FilterFormValues>({
    resolver: zodResolver(filterSchema),
    defaultValues: defaultFormValues,
  })

  const searchValue = watch('search')
  const watchStatus = watch('status')

  const isFilterWatch = watchStatus !== 'ALL'

  const debouncedSearchValue = useDebouncedValue(searchValue, 500)

  function handleApplyFilters(data: FilterFormValues) {
    if (onChangeStatus && data.status) onChangeStatus(data.status)

    setIsFilter(isFilterWatch)
    setIsOpenFilter(false)
  }

  function handleClearFilters() {
    reset(defaultFormValues)

    if (onChangeStatus) onChangeStatus('ALL')
    if (onChangeSearch) onChangeSearch('')

    setIsFilter(false)
    setIsOpenFilter(false)
  }

  function handleChangeFilter() {
    setIsOpenFilter((state) => !state)
  }

  function handleSelectOpen() {
    isSelectOpenRef.current = true
  }

  function handleSelectClose() {
    setTimeout(() => {
      isSelectOpenRef.current = false
    }, 100)
  }

  useEffect(() => {
    if (!initializedRef.current) {
      initializedRef.current = true
      return
    }

    if (contextSearch === '' && contextStatus === 'ALL') {
      reset(defaultFormValues)
      setIsFilter(false)
      setIsOpenFilter(false)
    }
  }, [contextSearch, contextStatus, reset])

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (isSelectOpenRef.current) return

      const target = event.target as Node

      const isAutocompleteElement = (node: Node): boolean => {
        if (node instanceof HTMLElement) {
          if (
            node.classList.contains('optionList') ||
            node.classList.contains('autocomplete') ||
            node.closest('[data-autocomplete]') ||
            node.closest("[role='listbox']") ||
            node.closest("[role='option']")
          ) {
            return true
          }
        }
        return false
      }

      if (isAutocompleteElement(target)) {
        return
      }

      if (
        filterRef.current?.contains(target) ||
        filterToggleButtonMobileRef.current?.contains(target) ||
        filterToggleButtonRef.current?.contains(target)
      ) {
        return
      }

      setIsOpenFilter(false)
    }

    if (isOpenFilter) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isOpenFilter])

  useEffect(() => {
    if (onChangeSearch) {
      onChangeSearch(debouncedSearchValue ?? '')
    }
  }, [debouncedSearchValue, onChangeSearch])

  useEffect(() => {
    setCurrentPage(pageKey)
  }, [pageKey, setCurrentPage])

  return (
    <form
      onSubmit={handleSubmit(handleApplyFilters)}
      className="flex w-full flex-col space-y-4 xl:px-0"
    >
      <div className="flex space-x-6">
        <div className="w-full md:w-96 [&_button.at-rounded-component-iconButton-border-radius]:hidden">
          <Controller
            name="search"
            control={control}
            render={({ field }) => (
              <Search
                size="md"
                placeholder={placeholder}
                className="bg-ctx-interactive-secondary [&_button.at-rounded-component-iconButton-border-radius]:hidden"
                handleChange={field.onChange}
                query={field.value ?? ''}
              />
            )}
          />
        </div>

        <div className="relative">
          <Button
            ref={filterToggleButtonRef}
            hierarchy="secondary"
            trailingIcon={CiFilter}
            onClick={handleChangeFilter}
            className={`hidden bg-ctx-interactive-secondary ${isFilter && 'border border-ctx-highlight-focus shadow-[0_0_0_4px_#FFF8D4]'} md:flex`}
          >
            Filtros
          </Button>

          <IconButton
            ref={filterToggleButtonMobileRef}
            ariaLabel="Abrir filtros"
            hierarchy="secondary"
            icon={CiFilter}
            size="md"
            className={`bg-ctx-interactive-secondary md:hidden ${isFilter && 'border border-ctx-highlight-focus shadow-[0_0_0_4px_#FFF8D4]'}`}
            onClick={handleChangeFilter}
          />

          <AnimatePresence>
            {isOpenFilter && (
              <motion.div
                ref={filterRef}
                initial={{ opacity: 0, scale: 0.95, y: -10 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.95, y: -10 }}
                transition={{ duration: 0.2, ease: 'easeOut' }}
                className="absolute right-0 top-full z-40 mt-2 w-72 space-y-4 rounded-lg border bg-ctx-layout-spotlight p-4 shadow-lg"
              >
                <div className="z-50 flex flex-col space-y-4 md:flex-row md:space-x-6 md:space-y-0">
                  {onChangeStatus && (
                    <Controller
                      name="status"
                      control={control}
                      render={({ field }) => (
                        <SelectInput
                          label="Status"
                          size="md"
                          fullWidth
                          custom={{
                            content: 'z-50',
                          }}
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                          value={field.value}
                          onOpenChange={(open) => {
                            if (open) handleSelectOpen()
                            else handleSelectClose()
                          }}
                        >
                          {statusOptions.map((option) => (
                            <SelectInputItem
                              key={option.label}
                              value={String(option.value)}
                            >
                              {option.label}
                            </SelectInputItem>
                          ))}
                        </SelectInput>
                      )}
                    />
                  )}
                </div>

                <div className="flex flex-col items-center space-y-5 md:flex-row md:items-baseline md:justify-end md:space-x-6">
                  <LinkButton
                    hierarchy="secondary"
                    onClick={handleClearFilters}
                  >
                    Limpar Filtros
                  </LinkButton>
                  <Button type="submit" className="w-full md:w-32">
                    Aplicar
                  </Button>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </form>
  )
}
