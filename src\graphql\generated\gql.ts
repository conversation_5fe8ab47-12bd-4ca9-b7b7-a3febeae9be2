/* eslint-disable */
import * as types from './graphql';
import { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';

/**
 * Map of all GraphQL operations in the project.
 *
 * This map has several performance disadvantages:
 * 1. It is not tree-shakeable, so it will include all operations in the project.
 * 2. It is not minifiable, so the string of a GraphQL query will be multiple times inside the bundle.
 * 3. It does not support dead code elimination, so it will add unused operations.
 *
 * Therefore it is highly recommended to use the babel or swc plugin for production.
 * Learn more about it here: https://the-guild.dev/graphql/codegen/plugins/presets/preset-client#reducing-bundle-size
 */
type Documents = {
    "\n  query getEnrollmentsByCompanyId($criteria: String!) {\n    company(criteria: $criteria) {\n      enrollments {\n        enrollment_id\n      }\n    }\n  }\n": typeof types.GetEnrollmentsByCompanyIdDocument,
    "\n  query getLicenseActivesLMS {\n    userEnrollments {\n      id\n      type\n    }\n  }\n": typeof types.GetLicenseActivesLmsDocument,
    "\n  query getUserProfile {\n    profile {\n      payment_plan {\n        slug\n      }\n    }\n  }\n": typeof types.GetUserProfileDocument,
    "\n  mutation CreateStudyPlan(\n    $name: String!\n    $description: String!\n    $courses: [StudyPlanCoursePivot!]!\n    $company_id: Float!\n    $squad_ids: [Float!]\n    $user_ids: [Float!]\n    $is_pdi: Boolean\n    $is_for_all_users: Boolean\n    $is_for_all_squads: Boolean\n    $is_for_all_courses: Boolean\n  ) {\n    createStudyPlan(\n      data: {\n        name: $name\n        description: $description\n        courses: $courses\n        company_id: $company_id\n        squad_ids: $squad_ids\n        user_ids: $user_ids\n        is_pdi: $is_pdi\n        is_for_all_users: $is_for_all_users\n        is_for_all_squads: $is_for_all_squads\n        is_for_all_courses: $is_for_all_courses\n      }\n    ) {\n      id\n    }\n  }\n": typeof types.CreateStudyPlanDocument,
    "\n  mutation deleteStudyPlan($id: Float!) {\n    deleteStudyPlan(id: $id)\n  }\n": typeof types.DeleteStudyPlanDocument,
    "\n  query getStudyPlans(\n    $user_id: Float\n    $name: String\n    $company_id: Float!\n    $limit: Float\n    $page: Float\n    $onlyPDI: Boolean\n    $status: Boolean\n    $end_date: DateTime\n  ) {\n    studyPlans(\n      name: $name\n      company_id: $company_id\n      limit: $limit\n      page: $page\n      onlyPDI: $onlyPDI\n      user_id: $user_id\n      status: $status\n      end_date: $end_date\n    ) {\n      data {\n        courses_pivot {\n          course_id\n          course {\n            id\n            title\n          }\n        }\n        ai_generated\n        end_date\n        status\n        id\n        name\n        coursesCount\n        squadsCount\n        usersCount\n      }\n      total\n      perPage\n    }\n  }\n": typeof types.GetStudyPlansDocument,
};
const documents: Documents = {
    "\n  query getEnrollmentsByCompanyId($criteria: String!) {\n    company(criteria: $criteria) {\n      enrollments {\n        enrollment_id\n      }\n    }\n  }\n": types.GetEnrollmentsByCompanyIdDocument,
    "\n  query getLicenseActivesLMS {\n    userEnrollments {\n      id\n      type\n    }\n  }\n": types.GetLicenseActivesLmsDocument,
    "\n  query getUserProfile {\n    profile {\n      payment_plan {\n        slug\n      }\n    }\n  }\n": types.GetUserProfileDocument,
    "\n  mutation CreateStudyPlan(\n    $name: String!\n    $description: String!\n    $courses: [StudyPlanCoursePivot!]!\n    $company_id: Float!\n    $squad_ids: [Float!]\n    $user_ids: [Float!]\n    $is_pdi: Boolean\n    $is_for_all_users: Boolean\n    $is_for_all_squads: Boolean\n    $is_for_all_courses: Boolean\n  ) {\n    createStudyPlan(\n      data: {\n        name: $name\n        description: $description\n        courses: $courses\n        company_id: $company_id\n        squad_ids: $squad_ids\n        user_ids: $user_ids\n        is_pdi: $is_pdi\n        is_for_all_users: $is_for_all_users\n        is_for_all_squads: $is_for_all_squads\n        is_for_all_courses: $is_for_all_courses\n      }\n    ) {\n      id\n    }\n  }\n": types.CreateStudyPlanDocument,
    "\n  mutation deleteStudyPlan($id: Float!) {\n    deleteStudyPlan(id: $id)\n  }\n": types.DeleteStudyPlanDocument,
    "\n  query getStudyPlans(\n    $user_id: Float\n    $name: String\n    $company_id: Float!\n    $limit: Float\n    $page: Float\n    $onlyPDI: Boolean\n    $status: Boolean\n    $end_date: DateTime\n  ) {\n    studyPlans(\n      name: $name\n      company_id: $company_id\n      limit: $limit\n      page: $page\n      onlyPDI: $onlyPDI\n      user_id: $user_id\n      status: $status\n      end_date: $end_date\n    ) {\n      data {\n        courses_pivot {\n          course_id\n          course {\n            id\n            title\n          }\n        }\n        ai_generated\n        end_date\n        status\n        id\n        name\n        coursesCount\n        squadsCount\n        usersCount\n      }\n      total\n      perPage\n    }\n  }\n": types.GetStudyPlansDocument,
};

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 *
 *
 * @example
 * ```ts
 * const query = graphql(`query GetUser($id: ID!) { user(id: $id) { name } }`);
 * ```
 *
 * The query argument is unknown!
 * Please regenerate the types.
 */
export function graphql(source: string): unknown;

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getEnrollmentsByCompanyId($criteria: String!) {\n    company(criteria: $criteria) {\n      enrollments {\n        enrollment_id\n      }\n    }\n  }\n"): (typeof documents)["\n  query getEnrollmentsByCompanyId($criteria: String!) {\n    company(criteria: $criteria) {\n      enrollments {\n        enrollment_id\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getLicenseActivesLMS {\n    userEnrollments {\n      id\n      type\n    }\n  }\n"): (typeof documents)["\n  query getLicenseActivesLMS {\n    userEnrollments {\n      id\n      type\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getUserProfile {\n    profile {\n      payment_plan {\n        slug\n      }\n    }\n  }\n"): (typeof documents)["\n  query getUserProfile {\n    profile {\n      payment_plan {\n        slug\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation CreateStudyPlan(\n    $name: String!\n    $description: String!\n    $courses: [StudyPlanCoursePivot!]!\n    $company_id: Float!\n    $squad_ids: [Float!]\n    $user_ids: [Float!]\n    $is_pdi: Boolean\n    $is_for_all_users: Boolean\n    $is_for_all_squads: Boolean\n    $is_for_all_courses: Boolean\n  ) {\n    createStudyPlan(\n      data: {\n        name: $name\n        description: $description\n        courses: $courses\n        company_id: $company_id\n        squad_ids: $squad_ids\n        user_ids: $user_ids\n        is_pdi: $is_pdi\n        is_for_all_users: $is_for_all_users\n        is_for_all_squads: $is_for_all_squads\n        is_for_all_courses: $is_for_all_courses\n      }\n    ) {\n      id\n    }\n  }\n"): (typeof documents)["\n  mutation CreateStudyPlan(\n    $name: String!\n    $description: String!\n    $courses: [StudyPlanCoursePivot!]!\n    $company_id: Float!\n    $squad_ids: [Float!]\n    $user_ids: [Float!]\n    $is_pdi: Boolean\n    $is_for_all_users: Boolean\n    $is_for_all_squads: Boolean\n    $is_for_all_courses: Boolean\n  ) {\n    createStudyPlan(\n      data: {\n        name: $name\n        description: $description\n        courses: $courses\n        company_id: $company_id\n        squad_ids: $squad_ids\n        user_ids: $user_ids\n        is_pdi: $is_pdi\n        is_for_all_users: $is_for_all_users\n        is_for_all_squads: $is_for_all_squads\n        is_for_all_courses: $is_for_all_courses\n      }\n    ) {\n      id\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation deleteStudyPlan($id: Float!) {\n    deleteStudyPlan(id: $id)\n  }\n"): (typeof documents)["\n  mutation deleteStudyPlan($id: Float!) {\n    deleteStudyPlan(id: $id)\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getStudyPlans(\n    $user_id: Float\n    $name: String\n    $company_id: Float!\n    $limit: Float\n    $page: Float\n    $onlyPDI: Boolean\n    $status: Boolean\n    $end_date: DateTime\n  ) {\n    studyPlans(\n      name: $name\n      company_id: $company_id\n      limit: $limit\n      page: $page\n      onlyPDI: $onlyPDI\n      user_id: $user_id\n      status: $status\n      end_date: $end_date\n    ) {\n      data {\n        courses_pivot {\n          course_id\n          course {\n            id\n            title\n          }\n        }\n        ai_generated\n        end_date\n        status\n        id\n        name\n        coursesCount\n        squadsCount\n        usersCount\n      }\n      total\n      perPage\n    }\n  }\n"): (typeof documents)["\n  query getStudyPlans(\n    $user_id: Float\n    $name: String\n    $company_id: Float!\n    $limit: Float\n    $page: Float\n    $onlyPDI: Boolean\n    $status: Boolean\n    $end_date: DateTime\n  ) {\n    studyPlans(\n      name: $name\n      company_id: $company_id\n      limit: $limit\n      page: $page\n      onlyPDI: $onlyPDI\n      user_id: $user_id\n      status: $status\n      end_date: $end_date\n    ) {\n      data {\n        courses_pivot {\n          course_id\n          course {\n            id\n            title\n          }\n        }\n        ai_generated\n        end_date\n        status\n        id\n        name\n        coursesCount\n        squadsCount\n        usersCount\n      }\n      total\n      perPage\n    }\n  }\n"];

export function graphql(source: string) {
  return (documents as any)[source] ?? {};
}

export type DocumentType<TDocumentNode extends DocumentNode<any, any>> = TDocumentNode extends DocumentNode<  infer TType,  any>  ? TType  : never;