import { Checkbox, Textarea, TextField } from '@ads/components-react'
import { HiOutlineClipboardDocumentCheck } from 'react-icons/hi2'

import { SelectAds } from '@/components/ui/select-ads'

export function Step1() {
  return (
    <div className="min-w-full space-y-3 rounded-lg bg-white p-8">
      <h1 className="flex items-center gap-2 text-ctx-content-title ts-heading-sm">
        <HiOutlineClipboardDocumentCheck />
        Informações Básicas
      </h1>

      <section className="w-full space-y-8 rounded-xl border border-border p-4">
        <TextField
          fullWidth
          label="Nome"
          type="text"
          placeholder="Nome da trilha de apendizagem"
          custom={{ input: 'h-11' }}
        />

        <Textarea
          fullWidth
          label="Descrição"
          placeholder="Descrição da trilha de aprendizagem"
          rows={6}
        />

        <div>
          <Checkbox label="Trilha obrigatória" />

          <SelectAds
            options={[]}
            label="Selecionar duração"
            placeholder="Carga horária"
          />
        </div>
      </section>
    </div>
  )
}
