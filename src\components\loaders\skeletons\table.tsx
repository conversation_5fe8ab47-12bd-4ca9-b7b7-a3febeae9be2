import { TableColumn } from '@/components/ui/data-table/types'
import { TableCell, TableRow } from '@/components/ui/table'

export function TableSkeleton({ columns }: { columns: TableColumn[] }) {
  return (
    <>
      {Array.from({ length: 5 }).map((_, rowIndex) => (
        <TableRow key={rowIndex}>
          {columns.map((column, colIndex) => (
            <TableCell key={colIndex} className={column.className}>
              <div className="h-4 animate-pulse rounded bg-muted" />
            </TableCell>
          ))}
        </TableRow>
      ))}
    </>
  )
}
